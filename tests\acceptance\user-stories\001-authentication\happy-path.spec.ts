import { test, expect } from '@playwright/test';
import { AuthPage } from '../../page-objects/auth-page';
import { testUsers, generateUniqueEmail, generateBusinessName, performanceTargets } from '../../fixtures/test-data';

test.describe('User Story 001: Authentication Happy Path', () => {
  let authPage: AuthPage;
  let uniqueEmail: string;
  let businessName: string;

  test.beforeEach(async ({ page }) => {
    authPage = new AuthPage(page);
    uniqueEmail = generateUniqueEmail();
    businessName = generateBusinessName();
    
    // Navigate to auth page
    await authPage.goto();
  });

  test('Scenario 1: Complete registration and login flow', async ({ page }) => {
    // Step 1: Measure page load performance
    const pageLoadTime = await authPage.measurePageLoad();
    expect(pageLoadTime).toBeLessThan(performanceTargets.pageLoad);
    console.log(`📊 Page load time: ${pageLoadTime}ms`);

    // Step 2: Navigate to signup tab
    await authPage.goToSignup();
    
    // Step 3: Fill out registration form with valid data
    await authPage.fillSignupForm({
      email: uniqueEmail,
      password: testUsers.validUser.password,
      businessName: businessName,
      acceptTerms: true
    });

    // Step 4: Measure form validation performance
    const validationTime = await authPage.measureFormValidation();
    expect(validationTime).toBeLessThan(performanceTargets.formValidation);
    console.log(`📊 Form validation time: ${validationTime}ms`);

    // Step 5: Submit registration
    const registrationStartTime = Date.now();
    await authPage.submitSignup();
    
    // Step 6: Verify success message appears
    await authPage.expectSuccessMessage('Account created successfully');
    
    // Step 7: Measure registration API response time
    const registrationTime = Date.now() - registrationStartTime;
    expect(registrationTime).toBeLessThan(performanceTargets.apiResponse);
    console.log(`📊 Registration API time: ${registrationTime}ms`);

    // Step 8: Verify user is redirected or shown next steps
    // Note: In real implementation, this might redirect to email verification
    // or directly to the dashboard depending on email verification requirements
    
    // For now, let's verify we can navigate to login
    await authPage.goToLogin();

    // Step 9: Test login with the newly created account
    await authPage.fillLoginForm(uniqueEmail, testUsers.validUser.password);
    
    // Step 10: Submit login
    const loginStartTime = Date.now();
    await authPage.submitLogin();
    
    // Step 11: Verify successful login
    // This will depend on your app's behavior after login
    // For now, let's check that we don't see error messages
    await expect(authPage.errorMessage).not.toBeVisible();
    
    // Step 12: Measure login API response time
    const loginTime = Date.now() - loginStartTime;
    expect(loginTime).toBeLessThan(performanceTargets.apiResponse);
    console.log(`📊 Login API time: ${loginTime}ms`);

    // Step 13: Verify we're redirected to the main application
    // This will depend on your routing setup
    await page.waitForURL(/\/(dashboard|home|main)/, { timeout: 5000 });
  });

  test('Scenario 1a: Registration form validation works correctly', async () => {
    await authPage.goToSignup();
    
    // Test email validation
    await authPage.emailInput.fill('invalid-email');
    await authPage.emailInput.blur();
    await authPage.expectEmailError('Please enter a valid email address');
    
    // Test password validation
    await authPage.passwordInput.fill('weak');
    await authPage.passwordInput.blur();
    await authPage.expectPasswordError();
    
    // Test business name validation
    await authPage.businessNameInput.fill('');
    await authPage.businessNameInput.blur();
    await authPage.expectErrorMessage(); // Should show business name required
    
    // Test terms acceptance
    await authPage.fillSignupForm({
      email: uniqueEmail,
      password: testUsers.validUser.password,
      businessName: businessName,
      acceptTerms: false
    });
    
    await authPage.submitSignup();
    await authPage.expectErrorMessage(); // Should show terms acceptance required
  });

  test('Scenario 1b: Login form validation works correctly', async () => {
    await authPage.goToLogin();
    
    // Test empty form submission
    await authPage.submitLogin();
    await authPage.expectErrorMessage(); // Should show validation errors
    
    // Test invalid email format
    await authPage.fillLoginForm('invalid-email', 'password');
    await authPage.submitLogin();
    await authPage.expectEmailError('Please enter a valid email address');
    
    // Test empty password
    await authPage.fillLoginForm(uniqueEmail, '');
    await authPage.submitLogin();
    await authPage.expectErrorMessage(); // Should show password required
  });

  test('Scenario 1c: Cross-platform responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await authPage.goto();
    
    // Verify forms are usable on mobile
    await authPage.goToSignup();
    await expect(authPage.signupButton).toBeVisible();
    await expect(authPage.emailInput).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await authPage.goto();
    
    // Verify forms are usable on tablet
    await authPage.goToLogin();
    await expect(authPage.loginButton).toBeVisible();
    await expect(authPage.loginEmailInput).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1280, height: 720 });
    await authPage.goto();
    
    // Verify forms are usable on desktop
    await expect(authPage.signupTab).toBeVisible();
    await expect(authPage.loginTab).toBeVisible();
  });

  test('Scenario 1d: Accessibility compliance', async ({ page }) => {
    await authPage.goto();
    
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await expect(authPage.signupTab).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(authPage.loginTab).toBeFocused();
    
    // Test form accessibility
    await authPage.goToSignup();
    
    // Verify form labels are properly associated
    await expect(authPage.emailInput).toHaveAttribute('aria-label');
    await expect(authPage.passwordInput).toHaveAttribute('aria-label');
    await expect(authPage.businessNameInput).toHaveAttribute('aria-label');
    
    // Test error message accessibility
    await authPage.emailInput.fill('invalid');
    await authPage.emailInput.blur();
    await authPage.expectFormValidation();
    
    // Verify error messages are announced to screen readers
    await expect(authPage.emailError).toHaveAttribute('role', 'alert');
  });
});
