-- RLS-Only Setup for Strata Compliance
-- This script only configures RLS policies in the public schema
-- Run this in Supabase SQL Editor

-- ============================================================================
-- 1. ENABLE ROW LEVEL SECURITY
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE sites ENABLE ROW LEVEL SECURITY;
ALTER TABLE inspections ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 2. DROP EXISTING POLICIES (if any)
-- ============================================================================

-- Tenants policies
DROP POLICY IF EXISTS "Users can view their own tenant" ON tenants;
DROP POLICY IF EXISTS "Only tenant admins can update tenant details" ON tenants;

-- Tenant users policies
DROP POLICY IF EXISTS "Users can view their own tenant memberships" ON tenant_users;
DROP POLICY IF EXISTS "Tenant admins can manage tenant users" ON tenant_users;
DROP POLICY IF EXISTS "Users can update their own profile" ON tenant_users;

-- Clients policies
DROP POLICY IF EXISTS "Users can view clients in their tenant" ON clients;
DROP POLICY IF EXISTS "Schedulers and admins can manage clients" ON clients;

-- Properties policies
DROP POLICY IF EXISTS "Users can view properties in their tenant" ON properties;
DROP POLICY IF EXISTS "Schedulers and admins can manage properties" ON properties;

-- Sites policies
DROP POLICY IF EXISTS "Users can view sites in their tenant" ON sites;
DROP POLICY IF EXISTS "Schedulers and admins can manage sites" ON sites;

-- Inspections policies
DROP POLICY IF EXISTS "Users can view inspections in their tenant" ON inspections;
DROP POLICY IF EXISTS "Inspectors can manage their assigned inspections" ON inspections;
DROP POLICY IF EXISTS "Schedulers and admins can manage all inspections" ON inspections;

-- ============================================================================
-- 3. CREATE RLS POLICIES
-- ============================================================================

-- TENANTS TABLE POLICIES
CREATE POLICY "Users can view their own tenant"
    ON tenants FOR SELECT
    USING (id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Only tenant admins can update tenant details"
    ON tenants FOR UPDATE
    USING (
        id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') = 'TenantAdmin'
    );

-- TENANT_USERS TABLE POLICIES
CREATE POLICY "Users can view their own tenant memberships"
    ON tenant_users FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Tenant admins can manage tenant users"
    ON tenant_users FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') = 'TenantAdmin'
    );

CREATE POLICY "Users can update their own profile"
    ON tenant_users FOR UPDATE
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND user_id = auth.uid()
    )
    WITH CHECK (
        -- Prevent users from changing their own role
        role = (SELECT role FROM tenant_users WHERE tenant_id = (auth.jwt() ->> 'tenant_id')::uuid AND user_id = auth.uid())
    );

-- CLIENTS TABLE POLICIES
CREATE POLICY "Users can view clients in their tenant"
    ON clients FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Schedulers and admins can manage clients"
    ON clients FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
    );

-- PROPERTIES TABLE POLICIES
CREATE POLICY "Users can view properties in their tenant"
    ON properties FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Schedulers and admins can manage properties"
    ON properties FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
    );

-- SITES TABLE POLICIES
CREATE POLICY "Users can view sites in their tenant"
    ON sites FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Schedulers and admins can manage sites"
    ON sites FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
    );

-- INSPECTIONS TABLE POLICIES
CREATE POLICY "Users can view inspections in their tenant"
    ON inspections FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Inspectors can manage their assigned inspections"
    ON inspections FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (
            (auth.jwt() ->> 'role') = 'Inspector' 
            AND assigned_inspector_id = auth.uid()
        )
    );

CREATE POLICY "Schedulers and admins can manage all inspections"
    ON inspections FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
    );

-- ============================================================================
-- 4. HELPER FUNCTIONS
-- ============================================================================

-- Function to get user tenant info
CREATE OR REPLACE FUNCTION get_user_tenant_info(user_id uuid)
RETURNS TABLE(tenant_id uuid, role text, first_name text, last_name text)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT tu.tenant_id, tu.role, tu.first_name, tu.last_name
  FROM tenant_users tu
  WHERE tu.user_id = get_user_tenant_info.user_id
  AND tu.status = 'active'
  LIMIT 1;
END;
$$;

-- Function to add user to tenant
CREATE OR REPLACE FUNCTION add_user_to_tenant(
  p_tenant_id uuid,
  p_user_id uuid,
  p_role text,
  p_first_name text DEFAULT NULL,
  p_last_name text DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_id uuid;
BEGIN
  INSERT INTO tenant_users (tenant_id, user_id, role, first_name, last_name, status)
  VALUES (p_tenant_id, p_user_id, p_role, p_first_name, p_last_name, 'active')
  RETURNING id INTO new_id;
  
  RETURN new_id;
END;
$$;

-- Function to check RLS status
CREATE OR REPLACE FUNCTION check_rls_status()
RETURNS TABLE(table_name text, rls_enabled boolean, policy_count bigint)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.tablename::text,
    t.rowsecurity,
    COUNT(p.policyname)
  FROM pg_tables t
  LEFT JOIN pg_policies p ON p.tablename = t.tablename AND p.schemaname = t.schemaname
  WHERE t.schemaname = 'public'
  AND t.tablename IN ('tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections')
  GROUP BY t.tablename, t.rowsecurity
  ORDER BY t.tablename;
END;
$$;

-- ============================================================================
-- 5. VERIFICATION QUERIES
-- ============================================================================

-- Check RLS status
SELECT * FROM check_rls_status();

-- Check policies exist
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;
