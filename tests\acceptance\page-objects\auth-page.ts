import { Page, Locator, expect } from '@playwright/test';

export class AuthPage {
  readonly page: Page;
  
  // Navigation
  readonly signupTab: Locator;
  readonly loginTab: Locator;
  
  // Signup form elements
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly confirmPasswordInput: Locator;
  readonly businessNameInput: Locator;
  readonly termsCheckbox: Locator;
  readonly signupButton: Locator;
  
  // Login form elements
  readonly loginEmailInput: Locator;
  readonly loginPasswordInput: Locator;
  readonly loginButton: Locator;
  readonly forgotPasswordLink: Locator;
  
  // Password reset form
  readonly resetEmailInput: Locator;
  readonly sendResetButton: Locator;
  readonly newPasswordInput: Locator;
  readonly confirmNewPasswordInput: Locator;
  readonly updatePasswordButton: Locator;
  
  // Messages and feedback
  readonly successMessage: Locator;
  readonly errorMessage: Locator;
  readonly emailError: Locator;
  readonly passwordError: Locator;
  readonly confirmPasswordError: Locator;
  readonly businessNameError: Locator;
  readonly termsError: Locator;
  
  // Loading states
  readonly loadingSpinner: Locator;
  readonly submitButtonLoading: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Navigation
    this.signupTab = page.locator('[data-testid="signup-tab"]');
    this.loginTab = page.locator('[data-testid="login-tab"]');
    
    // Signup form
    this.emailInput = page.locator('[data-testid="signup-email"]');
    this.passwordInput = page.locator('[data-testid="signup-password"]');
    this.confirmPasswordInput = page.locator('[data-testid="signup-confirm-password"]');
    this.businessNameInput = page.locator('[data-testid="signup-business-name"]');
    this.termsCheckbox = page.locator('[data-testid="signup-terms"]');
    this.signupButton = page.locator('[data-testid="signup-button"]');
    
    // Login form
    this.loginEmailInput = page.locator('[data-testid="login-email"]');
    this.loginPasswordInput = page.locator('[data-testid="login-password"]');
    this.loginButton = page.locator('[data-testid="login-button"]');
    this.forgotPasswordLink = page.locator('[data-testid="forgot-password-link"]');
    
    // Password reset
    this.resetEmailInput = page.locator('[data-testid="reset-email"]');
    this.sendResetButton = page.locator('[data-testid="send-reset-button"]');
    this.newPasswordInput = page.locator('[data-testid="new-password"]');
    this.confirmNewPasswordInput = page.locator('[data-testid="confirm-new-password"]');
    this.updatePasswordButton = page.locator('[data-testid="update-password-button"]');
    
    // Messages
    this.successMessage = page.locator('[data-testid="success-message"]');
    this.errorMessage = page.locator('[data-testid="error-message"]');
    this.emailError = page.locator('[data-testid="email-error"]');
    this.passwordError = page.locator('[data-testid="password-error"]');
    this.confirmPasswordError = page.locator('[data-testid="confirm-password-error"]');
    this.businessNameError = page.locator('[data-testid="business-name-error"]');
    this.termsError = page.locator('[data-testid="terms-error"]');
    
    // Loading states
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]');
    this.submitButtonLoading = page.locator('[data-testid="submit-loading"]');
  }

  // Navigation methods
  async goto() {
    await this.page.goto('/auth');
  }

  async goToSignup() {
    await this.signupTab.click();
  }

  async goToLogin() {
    await this.loginTab.click();
  }

  async goToForgotPassword() {
    await this.forgotPasswordLink.click();
  }

  // Signup methods
  async fillSignupForm(userData: {
    email: string;
    password: string;
    confirmPassword?: string;
    businessName: string;
    acceptTerms?: boolean;
  }) {
    await this.emailInput.fill(userData.email);
    await this.passwordInput.fill(userData.password);
    await this.confirmPasswordInput.fill(userData.confirmPassword || userData.password);
    await this.businessNameInput.fill(userData.businessName);
    
    if (userData.acceptTerms !== false) {
      await this.termsCheckbox.check();
    }
  }

  async submitSignup() {
    await this.signupButton.click();
  }

  async signup(userData: {
    email: string;
    password: string;
    confirmPassword?: string;
    businessName: string;
    acceptTerms?: boolean;
  }) {
    await this.fillSignupForm(userData);
    await this.submitSignup();
  }

  // Login methods
  async fillLoginForm(email: string, password: string) {
    await this.loginEmailInput.fill(email);
    await this.loginPasswordInput.fill(password);
  }

  async submitLogin() {
    await this.loginButton.click();
  }

  async login(email: string, password: string) {
    await this.fillLoginForm(email, password);
    await this.submitLogin();
  }

  // Password reset methods
  async requestPasswordReset(email: string) {
    await this.resetEmailInput.fill(email);
    await this.sendResetButton.click();
  }

  async resetPassword(newPassword: string, confirmPassword?: string) {
    await this.newPasswordInput.fill(newPassword);
    await this.confirmNewPasswordInput.fill(confirmPassword || newPassword);
    await this.updatePasswordButton.click();
  }

  // Validation methods
  async expectSuccessMessage(message?: string) {
    await expect(this.successMessage).toBeVisible();
    if (message) {
      await expect(this.successMessage).toContainText(message);
    }
  }

  async expectErrorMessage(message?: string) {
    await expect(this.errorMessage).toBeVisible();
    if (message) {
      await expect(this.errorMessage).toContainText(message);
    }
  }

  async expectEmailError(message?: string) {
    await expect(this.emailError).toBeVisible();
    if (message) {
      await expect(this.emailError).toContainText(message);
    }
  }

  async expectPasswordError(message?: string) {
    await expect(this.passwordError).toBeVisible();
    if (message) {
      await expect(this.passwordError).toContainText(message);
    }
  }

  async expectFormValidation() {
    // Wait for form validation to complete
    await this.page.waitForTimeout(200);
  }

  // Performance methods
  async measurePageLoad(): Promise<number> {
    const startTime = Date.now();
    await this.page.waitForLoadState('networkidle');
    return Date.now() - startTime;
  }

  async measureFormValidation(): Promise<number> {
    const startTime = Date.now();
    await this.emailInput.blur();
    await this.expectFormValidation();
    return Date.now() - startTime;
  }
}
