{"name": "strata-compliance", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.2.5", "turbo": "^2.5.4", "typescript": "^5.3.3"}, "packageManager": "pnpm@8.15.4", "engines": {"node": ">=18.0.0"}}