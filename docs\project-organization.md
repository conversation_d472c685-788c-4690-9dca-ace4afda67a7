# Project Organization Guidelines

**Goal**: Maintain a clean, non-duplicated codebase with clear single sources of truth.

## 📋 Documentation Rules

### Single Source of Truth (SSOT)

- **One authoritative document per topic** - no duplicates
- **Link to SSOT** from other locations, don't copy content
- **Update in one place only** - content changes happen at the source

### Documentation Hierarchy

```
docs/
├── README.md                           # Quick start guide for new agents
├── project-plan-and-cursor-rules.md   # Main project reference (SSOT)
├── acceptance-testing-process.md       # Acceptance testing workflow for remote agents
├── implementation-checklist.md        # Progress tracking (UPDATE WEEKLY)
├── project-organization.md            # This file - org guidelines
├── user-functionality-analysis.md     # Strategic context and market analysis
├── competitive-feature-analysis.md    # Competitor workflow analysis
├── license-tier-strategy.md           # Business model and tier design
├── pricing-strategy-analysis.md       # Per-seat licensing and enterprise value
├── user-journey-flows.md              # Detailed UX workflows
├── implementation-roadmap.md          # 18-month development plan
├── technical-architecture-plan.md     # Technical implementation (ALIGNED)
├── architecture-alignment-summary.md  # Corrections made to align with project plan
├── database-implementation-summary.md # Database schema and implementation details
├── development-setup.md               # Complete development environment setup
├── developer-onboarding-checklist.md  # Step-by-step setup verification
└── spotlite/                          # Design reference screenshots
    ├── index.html                     # Organized interface analysis
    └── *.png                          # Spotlite Compliance screenshots

database/
├── SETUP.md                           # Database setup (SSOT)
└── README.md                          # Brief overview + links

packages/[package]/
└── README.md                          # Package-specific documentation
```

### Documentation Standards

- **README.md**: Brief overview + links to detailed docs
- **SETUP.md**: Complete setup/installation instructions
- **No duplicate setup guides** across directories
- **Link rather than copy** - use relative links to reference other docs

## 🗂️ Code Organization Rules

### No Duplicate Code

- **Shared utilities** → `packages/core/src/utils/`
- **Shared types** → `packages/core/src/types/`
- **Shared constants** → `packages/core/src/constants/`

### Script Organization

- **Database scripts** → `scripts/` directory only
- **One script per purpose** (test, setup, seed, etc.)
- **No duplicate scripts** in different directories

### File Naming Conventions

- **Scripts**: `kebab-case.js` (e.g., `test-db.js`, `setup-staging.js`)
- **Docs**: `kebab-case.md` (e.g., `project-organization.md`)
- **Components**: `PascalCase.tsx` (e.g., `DatabaseService.tsx`)

## 🚫 Anti-Patterns to Avoid

### Documentation Anti-Patterns

- ❌ Multiple setup guides for the same topic
- ❌ Copying content between docs instead of linking
- ❌ Setup instructions in multiple places
- ❌ Outdated duplicate documentation

### Code Anti-Patterns

- ❌ Same utility function in multiple packages
- ❌ Duplicate test scripts
- ❌ Copy-pasted configuration files
- ❌ Multiple files doing the same job

## ✅ Best Practices

### When Creating New Documentation

1. **Check if SSOT exists** - search for existing docs on the topic
2. **Update SSOT** if it exists, don't create new docs
3. **Link to SSOT** from relevant places
4. **Use clear hierarchy** - overview → detailed docs

### When Creating New Code

1. **Check for existing utilities** in `packages/core`
2. **Add to existing service** rather than creating new ones
3. **Use shared types** from core package
4. **Follow established patterns** in the codebase

### When Working on User Stories

1. **Follow acceptance testing process** - see `docs/acceptance-testing-process.md`
2. **Record task scenarios** before implementation
3. **Create Playwright tests** for all acceptance criteria
4. **Validate with local testing** before marking complete

### Before Committing

- [ ] No duplicate documentation exists
- [ ] No duplicate code/utilities
- [ ] Links work and point to correct SSoT
- [ ] New files follow naming conventions

## 🔧 Maintenance

### Regular Reviews

- **Monthly**: Review for duplicate docs and outdated links
- **Per feature**: Check for code duplication during PR reviews
- **Release**: Verify all documentation is up-to-date

### Tools

- Use eslint rules to prevent duplicate code
- Use link checkers for documentation
- Regular grep searches for duplicate content

---

**Remember**: When in doubt, link to existing content rather than copying it.
